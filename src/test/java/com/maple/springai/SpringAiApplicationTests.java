package com.maple.springai;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.TreeMap;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

@SpringBootTest
class SpringAiApplicationTests {

    @Test
    void contextLoads() {
        LinkedHashMap<Object, Object> LinkedHashMap = new LinkedHashMap<>();
        LinkedHashMap.put("1","1");
        LinkedHashMap.put("2","2");
        LinkedHashMap.put("3","3");
        LinkedHashMap.put("4","4");
        LinkedHashMap.put("5","5");
        LinkedHashMap.put("6","6");
        LinkedHashMap.put("7","7");
        LinkedHashMap.put("8","8");
        LinkedHashMap.put("9","9");
        LinkedHashMap.put("10","10");
        System.out.println(LinkedHashMap);
        TreeMap<Object, Object> objectObjectTreeMap = new TreeMap<>();
        objectObjectTreeMap.put("1","1");
        objectObjectTreeMap.put("2","2");
        objectObjectTreeMap.put("3","3");
        System.out.println(objectObjectTreeMap);
    }
    @Test
    void test1() throws ExecutionException, InterruptedException {
        ExecutorService executorService = new ThreadPoolExecutor(
                5,
                10,
                1000,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        @Data
        @AllArgsConstructor
        class Person {
            private String name;
            private int age;
        }
        executorService.execute(()->{
            System.out.println("hello world");
        });
        Future<?> submit = executorService.submit(() -> {
            System.out.println("hello world");
            Person person = new Person("maple", 18);
            return person;
        });
        Person o = (Person)submit.get();
        System.out.println(o);
    }
    @Test
    void test2() throws InterruptedException {
        class Mythread extends Thread{
            @Override
            public void run() {
                System.out.println("=====hahha=====");
            }
        }
        Mythread mythread = new Mythread();
        mythread.start();
        class MyRunnable implements Runnable{
            @Override
            public void run() {
                System.out.println("===这是runnable===");
                if(mythread.isInterrupted()){
                    System.out.println("===线程被中断===");
                }
            }
        }
        Thread thread = new Thread(new MyRunnable());
        thread.start();
        class MyCallable implements Callable<String>{
            @Override
            public String call() throws Exception {
                return "===这是callable===";
            }
            public synchronized void stop(){
                System.out.println("===线程被中断==");
            }
        }
        Callable<String> callable = new MyCallable();
        FutureTask<String> futureTask = new FutureTask<>(callable);
        new Thread(futureTask).start();
    }
    @Test
    void test3() {
        @Data
        class Config {
            String version;
            public Config(String version) { this.version = version; }
        }
        AtomicReference<Config> configRef = new AtomicReference<>(new Config("v1"));

        // 线程A 更新配置
        configRef.set(new Config("v2"));

        // 线程B 读取配置
        Config cfg = configRef.get();
        System.out.println(cfg.version);

    }
    @Test
    void test4() {
        ArrayList<String> list = new ArrayList<>();
        list.add("1");
        list.add("2");
        list.add("3");
        list.add("4");
        list.add("5");
        String remove = String.valueOf(list.remove("3"));
        System.out.println(remove);
        System.out.println(list);
    }


}
