//package com.maple.springai.config;
//
//
//
//import io.milvus.client.MilvusServiceClient;
//import io.milvus.param.ConnectParam;
//import io.milvus.param.IndexType;
//import io.milvus.param.MetricType;
//
//
//import org.springframework.ai.embedding.EmbeddingModel;
//import org.springframework.ai.embedding.TokenCountBatchingStrategy;
//import org.springframework.ai.vectorstore.VectorStore;
//import org.springframework.ai.vectorstore.milvus.MilvusVectorStore;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//public class MilvusConfig {
//
//    @Bean
//    public VectorStore vectorStore(MilvusServiceClient milvusClient, EmbeddingModel embeddingModel) {
//        return MilvusVectorStore.builder(milvusClient, embeddingModel)
//                .collectionName("music_collections")
//                .databaseName("my_database_1")
//                .indexType(IndexType.IVF_FLAT)
//                .metricType(MetricType.COSINE)
//                .batchingStrategy(new TokenCountBatchingStrategy())
//                .initializeSchema(true)
//                .build();
//    }
//
//    @Bean
//    public MilvusServiceClient milvusClient() {
//        return new MilvusServiceClient(ConnectParam.newBuilder()
//                .withAuthorization("minioadmin", "minioadmin")
//                .withUri(milvusContainer.getEndpoint())
//                .build());
//    }
//
//}
