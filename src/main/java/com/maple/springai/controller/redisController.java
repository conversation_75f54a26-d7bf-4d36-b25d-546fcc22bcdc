package com.maple.springai.controller;

import lombok.RequiredArgsConstructor;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Set;

import static java.util.concurrent.TimeUnit.SECONDS;

@RestController
@RequiredArgsConstructor
public class redisController {

     private final StringRedisTemplate redisTemplate;
     private final RedissonClient redissonClient;
    @GetMapping("/reids-string")
    public String redisString() {
        redisTemplate.opsForValue()
                .setIfAbsent("redis-string", "redis-string", 60L, SECONDS);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("ONE", "V1");
        stringStringHashMap.put("TWO", "V2");
        stringStringHashMap.put("THREE", "V3");
        redisTemplate.opsForHash().put("hash-bigkey","hash-key",stringStringHashMap );
        redisTemplate.opsForHash().entries("hash-bigkey").forEach((k,v)->{
            System.out.println("key : " + k);
            System.out.println(k + " : " + v);
        });

        redisTemplate.opsForSet().add("set-bigkey","set-key1","set-key2","set-key3");
        String pop = redisTemplate.opsForSet().pop("set-bigkey").toString();
        System.out.println(pop);

        redisTemplate.opsForZSet().add("zset-bigkey","zset-key1",1.0);
        redisTemplate.opsForZSet().add("zset-bigkey","zset-key2",2.0);
        redisTemplate.opsForZSet().add("zset-bigkey","zset-key3",3.0);
        Double score = redisTemplate.opsForZSet().score("zset-bigkey", "zset-key1");
        Set set = redisTemplate.opsForZSet().popMax("zset-bigkey", 2);
        System.out.println(set);
        return "redis-string";
    }
}
