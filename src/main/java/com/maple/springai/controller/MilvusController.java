//package com.maple.springai.controller;
//
//
////import com.alipay.api.domain.AlipayTradePayModel;
//import lombok.RequiredArgsConstructor;
//import org.springframework.ai.document.Document;
//import org.springframework.ai.vectorstore.SearchRequest;
//import org.springframework.ai.vectorstore.VectorStore;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
//@RestController
//@RequiredArgsConstructor
//public class MilvusController {
//
////    private final MilvusClientV2 client;
//
////    @GetMapping("/add")
////    public void add() {
////        CreateDatabaseReq createDatabaseReq = CreateDatabaseReq.builder()
////                .databaseName("my_database_1")
////                .build();
////        client.createDatabase(createDatabaseReq);
////    }
////    private final VectorStore vectorStore;
////    @GetMapping("/get")
////    public List get() {
////
////
////        List <Document> documents = List.of(
////                new Document("Spring AI rocks!! Spring AI rocks!! Spring AI rocks!! Spring AI rocks!! Spring AI rocks!!", Map.of("meta1", "meta1")),
////                new Document("The World is Big and Salvation Lurks Around the Corner"),
////                new Document("You walk forward facing the past and you turn back toward the future.", Map.of("meta2", "meta2")));
////
////        // Add the documents to Milvus Vector Store
////        vectorStore.add(documents);
////
////    // Retrieve documents similar to a query
////        List<Document> results = this.vectorStore.similaritySearch(SearchRequest.builder().query("Spring").topK(5).build());
////        return results;
////    }
////    @GetMapping("/addCollections")
////    public void addCollections() {
////        // 3.1 Create schema
////        CreateCollectionReq.CollectionSchema schema = client.createSchema();
////
////        // 3.2 Add fields to schema
////        schema.addField(AddFieldReq.builder()
////                .fieldName("my_id")
////                .dataType(DataType.Int64)
////                .isPrimaryKey(true)
////                .autoID(false)
////                .build());
////
////        schema.addField(AddFieldReq.builder()
////                .fieldName("my_vector")
////                .dataType(DataType.FloatVector)
////                .dimension(5)
////                .build());
////
////        schema.addField(AddFieldReq.builder()
////                .fieldName("my_varchar")
////                .dataType(DataType.VarChar)
////                .maxLength(512)
////                .build());
////        // 3.3 Prepare index parameters
////        IndexParam indexParamForIdField = IndexParam.builder()
////                .fieldName("my_id")
////                .indexType(IndexParam.IndexType.AUTOINDEX)
////                .build();
////
////        IndexParam indexParamForVectorField = IndexParam.builder()
////                .fieldName("my_vector")
////                .indexType(IndexParam.IndexType.AUTOINDEX)
////                .metricType(IndexParam.MetricType.COSINE)
////                .build();
////
////        List<IndexParam> indexParams = new ArrayList<>();
////        indexParams.add(indexParamForIdField);
////        indexParams.add(indexParamForVectorField);
////        // 3.4 Create a collection with schema and index parameters
////        CreateCollectionReq customizedSetupReq1 = CreateCollectionReq.builder()
////                .collectionName("customized_setup_1")
////                .collectionSchema(schema)
////                .indexParams(indexParams)
////                .build();
////
////        client.createCollection(customizedSetupReq1);
////
////        // 3.5 Get load state of the collection
////        GetLoadStateReq customSetupLoadStateReq1 = GetLoadStateReq.builder()
////                .collectionName("customized_setup_1")
////                .build();
////
////        Boolean loaded = client.getLoadState(customSetupLoadStateReq1);
////        System.out.println(loaded);
////
//////        // 3.6 Create a collection and index it separately
//////        CreateCollectionReq customizedSetupReq2 = CreateCollectionReq.builder()
//////                .collectionName("customized_setup_2")
//////                .collectionSchema(schema)
//////                .build();
//////
//////        client.createCollection(customizedSetupReq2);
//////
//////        GetLoadStateReq customSetupLoadStateReq2 = GetLoadStateReq.builder()
//////                .collectionName("customized_setup_2")
//////                .build();
//////
//////        Boolean loaded = client.getLoadState(customSetupLoadStateReq2);
//////        System.out.println(loaded);
////    }
////    @GetMapping("/pay")
////    public String pay() {
////        ApiClient apiClient = Configuration.getDefaultApiClient();
////    //设置网关地址
////        apiClient.setBasePath(URL);
////    //设置alipayConfig参数（全局设置一次）
////        AlipayConfig alipayConfig = new AlipayConfig();
////    //设置应用ID
////        alipayConfig.setAppId(APPID);
////    //设置应用私钥
////        alipayConfig.setPrivateKey(PRIVATE_KEY);
////    //设置支付宝公钥
////        alipayConfig.setAlipayPublicKey(ALIPAY_PUBLIC_KEY);
////        apiClient.setAlipayConfig(alipayConfig);
////
////    //实例化客户端
////        AlipayTradeApi api = new AlipayTradeApi();
////    //调用 alipay.trade.pay
////        AlipayTradePayModel alipayTradePayModel = new AlipayTradePayModel()
////                .outTradeNo("20210817010101001")
////                .totalAmount("0.01")
////                .subject("测试商品")
////                .scene("bar_code")
////                .authCode("28763443825664394");
//////发起调用
////        AlipayTradePayResponseModel response = api.pay(alipayTradePayModel);
////    }
//
//}
