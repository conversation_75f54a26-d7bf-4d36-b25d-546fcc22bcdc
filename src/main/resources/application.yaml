spring:
  application:
    name: SpringAi
#  ai:
#    vectors tore:
#      milvus:
#        client:
#          host: "************"
#          port: 19530
##          username: "root"   # application.properties 配置方式
##          password: "milvus"
#        databaseName: "my_database_1"
#        collectionName: "music_collections"
#        embeddingDimension: 1536
#        indexType: IVF_FLAT
#        metricType: COSINE
#    openai:
#      api-key: sk-4bd01de71a3f46e4b1694263f5e06e4f
#      base-url: https://api.deepseek.com/v1   # DeepSeek 的 API 地址
#      embedding:
#        model: deepseek-embedding-001         # 示例模型
#      chat:
#        options:
#          model: deepseek-chat
  data:
    redis:
      host: ************
      port: 6379
      database: 0
      username: root
      password: 111111